"""
Validation tasks for Signal2Segment DAG.

This module contains tasks for validating configurations and query arguments
using AWS Lambda functions, following modern TaskFlow API patterns.
"""

import json
import logging
from datetime import timedelta
from typing import Dict, Any

from airflow.decorators import task
from airflow.exceptions import AirflowFailException
from airflow.providers.amazon.aws.operators.lambda_function import (
    LambdaInvokeFunctionOperator,
)

from dags.signal2segment.config import (
    get_signal2segment_config,
    validate_signal2segment_configuration,
)

logger = logging.getLogger(__name__)


@task(
    retries=3,
    retry_delay=timedelta(minutes=1),
)
def validate_configuration(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate a single configuration using AWS Lambda function.

    This task validates query arguments by invoking the quoteprospect Lambda function
    and ensures the configuration is valid before proceeding with audience generation.

    Args:
        config: Configuration dictionary containing audience type, query args, etc.

    Returns:
        Validated configuration dictionary

    Raises:
        AirflowFailException: If validation fails or <PERSON>da returns an error
    """
    logger.info(f"🔍 Validating configuration for org_id: {config.get('org_id')}")

    try:
        # Validate required fields
        required_fields = [
            "org_id",
            "audience_type",
            "query_args",
            "data_source",
            "audience_name_prefix",
            "destinations",
        ]

        for field in required_fields:
            if field not in config:
                raise ValueError(f"Missing required field: {field}")

        # Get signal2segment configuration
        signal2segment_config = get_signal2segment_config()
        query_map = signal2segment_config["query_map"]
        lambda_config = signal2segment_config["lambda_config"]

        audience_type = config["audience_type"]
        if audience_type not in query_map:
            available_types = ", ".join(query_map.keys())
            raise ValueError(
                f"Unknown audience_type '{audience_type}'. Available: {available_types}"
            )

        query_id = query_map[audience_type]
        logger.info(f"📋 Using query_id: {query_id} for audience_type: {audience_type}")

        # Prepare Lambda payload
        lambda_payload = {
            "query_id": query_id,
            "query_args": config["query_args"],
            "limit": lambda_config["validation_limit"],
        }

        logger.info(
            f"🚀 Invoking Lambda function for validation: {lambda_config['function_name']}"
        )

        # Invoke Lambda function for validation
        lambda_operator = LambdaInvokeFunctionOperator(
            task_id="lambda_validation_invoke",
            function_name=lambda_config["function_name"],
            invocation_type=lambda_config["invocation_type"],
            log_type=lambda_config["log_type"],
            aws_conn_id="aws_default",
            region_name=lambda_config["region_name"],
            payload=json.dumps(lambda_payload),
        )

        # Execute Lambda function
        lambda_response = lambda_operator.execute(context={})
        lambda_result = json.loads(lambda_response)

        logger.info(f"📊 Lambda response: {lambda_result}")

        # Check for errors in Lambda response
        if "error" in lambda_result:
            error_msg = lambda_result["error"]
            logger.error(f"❌ Lambda validation failed: {error_msg}")

            # Send notification about validation failure
            _send_validation_failure_notification(config, error_msg)

            raise AirflowFailException(f"Configuration validation failed: {error_msg}")

        # Validation successful
        logger.info(
            f"✅ Configuration validation successful for org_id: {config['org_id']}"
        )

        # Add validation metadata to config
        validated_config = config.copy()
        validated_config["validation_metadata"] = {
            "query_id": query_id,
            "lambda_response": lambda_result,
            "validated_at": "{{ ts }}",
        }

        return validated_config

    except ValueError as e:
        logger.error(f"📊 Configuration validation error: {e}")
        _send_validation_failure_notification(config, str(e))
        raise AirflowFailException(f"Configuration validation failed: {e}")

    except Exception as e:
        logger.error(f"❌ Unexpected error during validation: {e}")
        raise AirflowFailException(f"Validation task failed: {e}")


def _send_validation_failure_notification(
    config: Dict[str, Any], error_message: str
) -> None:
    """
    Send notification about validation failure.

    This function sends a Slack notification when configuration validation fails,
    providing details about the failed configuration and error message.

    Args:
        config: The configuration that failed validation
        error_message: The error message from validation
    """
    try:
        from airflow.providers.slack.operators.slack import SlackAPIPostOperator

        org_id = config.get("org_id", "unknown")
        audience_type = config.get("audience_type", "unknown")

        slack_message = (
            f"🚨 *Signal2Segment Configuration Validation Failed* 🚨\n\n"
            f"**Configuration Details:**\n"
            f"• Org ID: `{org_id}`\n"
            f"• Audience Type: `{audience_type}`\n"
            f"• Error: {error_message}\n\n"
            f"Please check the configuration and try again."
        )

        # Use environment-aware notification system
        from et_config import get_notification_configuration

        notification_config = get_notification_configuration("signal2segment")
        channel = notification_config["slack_channel"]

        slack_alert = SlackAPIPostOperator(
            task_id="validation_failure_slack_alert",
            username="Signal2Segment Validation",
            slack_conn_id="bi-report-slacker-token",
            text=slack_message,
            channel=channel,
        )

        slack_alert.execute(context={})
        logger.info(f"📱 Sent validation failure notification to {channel}")

    except Exception as e:
        logger.warning(f"⚠️ Failed to send validation failure notification: {e}")
        # Don't fail the task just because notification failed
