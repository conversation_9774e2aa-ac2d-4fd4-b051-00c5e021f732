"""
Environment registry and lookup functions for ETDAG framework.

This module provides centralized environment management including
environment detection, configuration lookup, and validation.
"""

import os
from typing import Dict, Optional, Any

from et_config.environments.dev import DEV_CONFIG
from et_config.environments.local import LOCAL_CONFIG
from et_config.environments.prod import PROD_CONFIG
from et_config.models.environment import EnvironmentConfig

# Environment registry
ENVIRONMENTS: Dict[str, EnvironmentConfig] = {
    "local": LOCAL_CONFIG,
    "dev": DEV_CONFIG,
    "prod": PROD_CONFIG,
}


def get_current_environment() -> str:
    """Get current environment from environment variables or default to local."""
    # Check various possible environment variable names
    env_vars = ["AIRFLOW_ENVIRONMENT", "ENVIRONMENT", "ENV", "DEPLOYMENT_ENV"]

    for var in env_vars:
        env = os.getenv(var)
        if env:
            return env.lower()

    # Check if we're in a testing environment or development context
    if (
        "pytest" in os.getenv("_", "")
        or "test" in os.getenv("PYTEST_CURRENT_TEST", "")
        or "python" in os.getenv("_", "")  # Running from python command
        or not os.getenv("AIRFLOW_HOME")  # No Airflow home set
    ):
        return "local"

    # Only try Airflow Variable if we're likely in an Airflow context
    try:
        # Quick check if airflow is available and configured
        from airflow.models import Variable

        return Variable.get("environment", default_var="local").lower()
    except Exception:
        # If anything fails with Airflow, default to local
        return "local"


def get_environment_config(env_name: Optional[str] = None) -> EnvironmentConfig:
    """Get configuration for specified environment or current environment."""
    if env_name is None:
        env_name = get_current_environment()

    config = ENVIRONMENTS.get(env_name)
    if not config:
        available = ", ".join(ENVIRONMENTS.keys())
        raise ValueError(f"Unknown environment '{env_name}'. Available: {available}")

    return config


def validate_environment_config(env_name: str) -> bool:
    """Validate that an environment configuration is complete and valid."""
    try:
        config = get_environment_config(env_name)

        # Basic validation checks
        assert config.name == env_name
        assert config.s3.bucket
        assert config.connections.starburst_conn_id
        assert config.iceberg_catalog
        assert config.hive_catalog
        assert len(config.allowed_schemas) > 0
        assert config.query_timeout > 0
        assert config.max_retries >= 0

        return True
    except Exception as e:
        print(f"Environment configuration validation failed for '{env_name}': {e}")
        return False


def add_environment(name: str, config: EnvironmentConfig) -> None:
    """Add a new environment configuration to the registry.

    Args:
        name: Environment name
        config: Environment configuration
    """
    ENVIRONMENTS[name] = config


def list_environments() -> list[str]:
    """Get list of available environment names."""
    return list(ENVIRONMENTS.keys())


def get_all_environments() -> Dict[str, EnvironmentConfig]:
    """Get all environment configurations."""
    return ENVIRONMENTS.copy()


# Configuration access functions with DAG-specific override support


def get_notification_configuration(
    environment: Optional[str] = None, dag_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get notification configuration for the specified or current environment.

    Args:
        environment: Environment name. If None, uses current environment.
        dag_name: DAG name for potential DAG-specific overrides (future use)

    Returns:
        Dictionary with notification configuration
    """
    if environment is None:
        environment = get_current_environment()

    env_config = get_environment_config(environment)

    return {
        "slack_webhook": env_config.notifications.slack_webhook,
        "opsgenie_api_key": env_config.notifications.opsgenie_api_key,
        "email_recipients": env_config.notifications.email_recipients,
    }


def validate_configuration(dag_name: str, environment: Optional[str] = None) -> bool:
    """
    Generic validation function that DAGs can override with their own logic.

    Args:
        dag_name: Name of the DAG to validate
        environment: Environment name. If None, uses current environment.

    Returns:
        True if configuration is valid, False otherwise
    """
    if environment is None:
        environment = get_current_environment()

    try:
        env_config = get_environment_config(environment)
        # Basic validation - just check if environment config exists
        return env_config is not None
    except Exception:
        return False


def list_available_configurations(environment: Optional[str] = None) -> Dict[str, list]:
    """
    List all available configurations for an environment.

    Args:
        environment: Environment name. If None, uses current environment.

    Returns:
        Dictionary with lists of available configuration types
    """
    if environment is None:
        environment = get_current_environment()

    env_config = get_environment_config(environment)

    return {
        "connections": {
            "starburst_conn_id": env_config.connections.starburst_conn_id,
            "pygene_conn_id": env_config.connections.pygene_conn_id,
            "slack_conn_id": env_config.connections.slack_conn_id,
        },
        "note": "All DAG-specific configurations have been moved to individual DAG folders",
    }
