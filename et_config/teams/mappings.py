"""
Team mappings for notifications and routing.

This module contains the mappings between teams and their
notification preferences including email, Slack, and OpsGenie.
"""

from typing import Dict, List

# Team email mappings
TEAM_EMAIL_MAPPING: Dict[str, List[str]] = {
    "data-engineering": ["<EMAIL>"],
    "analytics-engineering": ["<EMAIL>"],
    "business-intelligence": ["<EMAIL>"],
    "client-services": ["<EMAIL>"],
    "platform-team": ["<EMAIL>"],
    # DAG-specific email lists
    "signal2segment": ["<EMAIL>", "<EMAIL>"],
}

# Team Slack channel mappings
TEAM_SLACK_MAPPING: Dict[str, str] = {
    "data-engineering": "#data-engineering-guild",
    "analytics-engineering": "#analytics-engineering-guild",
    "business-intelligence": "#business-intelligence-guild",
    "client-services": "#client-alerts",
    "platform-team": "#platform-alerts",
    # DAG-specific channels
    "signal2segment": "#signal2segment-alerts",
}

# OpsGenie team mappings
OPSGENIE_TEAM_MAPPING: Dict[str, str] = {
    "data-engineering": "data-engineering",
    "analytics-engineering": "analytics-engineering",
    "business-intelligence": "business-intelligence",
    "platform-team": "platform-team",
    # DAG-specific OpsGenie routing
    "signal2segment": "data-engineering",  # Route to data-engineering team
}


def get_team_email_mapping() -> Dict[str, List[str]]:
    """Get the complete team email mapping."""
    return TEAM_EMAIL_MAPPING.copy()


def get_team_slack_mapping() -> Dict[str, str]:
    """Get the complete team Slack mapping."""
    return TEAM_SLACK_MAPPING.copy()


def get_opsgenie_mapping() -> Dict[str, str]:
    """Get the complete OpsGenie team mapping."""
    return OPSGENIE_TEAM_MAPPING.copy()


def add_team_email(team: str, emails: List[str]) -> None:
    """Add or update team email mapping.

    Args:
        team: Team name
        emails: List of email addresses
    """
    TEAM_EMAIL_MAPPING[team] = emails


def add_team_slack(team: str, channel: str) -> None:
    """Add or update team Slack channel mapping.

    Args:
        team: Team name
        channel: Slack channel (with or without #)
    """
    if not channel.startswith("#"):
        channel = f"#{channel}"
    TEAM_SLACK_MAPPING[team] = channel


def add_opsgenie_team(team: str, opsgenie_team: str) -> None:
    """Add or update OpsGenie team mapping.

    Args:
        team: Team name
        opsgenie_team: OpsGenie team name
    """
    OPSGENIE_TEAM_MAPPING[team] = opsgenie_team
