"""
Signal2Segment DAG Configuration

This module contains all signal2segment-specific configuration logic,
including validation and helper functions.
"""

from typing import Dict, Any, Optional

from et_config import (
    get_current_environment,
    get_environment_config,
)

# Signal2segment-specific query mappings (DAG-isolated)
SIGNAL2SEGMENT_QUERY_MAPS = {
    "local": {
        "AUDIENCE_TYPE_PROSPECTS_IN_AUTOMOBILE_MARKET": "d6c4dd02-7e57-11ee-b962-0242ac120002",
        "AUDIENCE_TYPE_PROSPECTS_BY_INTENT": "479f18c0-5ebf-448f-b7df-d3d3da978e54",
        "AUDIENCE_TYPE_PROSPECTS_LIKELY_HOME_SELLERS": "eff6f0ab-2149-4496-ad12-96d74bb7d672",
        "AUDIENCE_TYPE_PROSPECTS_IN_HOUSING_MARKET": "0813e754-daf0-4cc9-858e-db22e2701feb",
        "AUDIENCE_TYPE_PROSPECTS_IN_AUTOMOBILE_MARKET_V2": "e5168f2e-2a96-4a80-8b32-5f18653cbae9",
        "AUDIENCE_TYPE_PROSPECTS_BY_AUTOMOBILE_INTENT": "4f2cf7be-4fc1-4992-8663-22ccf114e86d",
        "AUDIENCE_TYPE_PROSPECTS_IN_AUTOMOBILE_MARKET_V3": "8d88d0c4-df8b-418d-b0a3-2a88d6d7a2ad",
    },
    "dev": {
        "AUDIENCE_TYPE_PROSPECTS_IN_AUTOMOBILE_MARKET": "d6c4dd02-7e57-11ee-b962-0242ac120002",
        "AUDIENCE_TYPE_PROSPECTS_BY_INTENT": "479f18c0-5ebf-448f-b7df-d3d3da978e54",
        "AUDIENCE_TYPE_PROSPECTS_LIKELY_HOME_SELLERS": "eff6f0ab-2149-4496-ad12-96d74bb7d672",
        "AUDIENCE_TYPE_PROSPECTS_IN_HOUSING_MARKET": "0813e754-daf0-4cc9-858e-db22e2701feb",
        "AUDIENCE_TYPE_PROSPECTS_IN_AUTOMOBILE_MARKET_V2": "e5168f2e-2a96-4a80-8b32-5f18653cbae9",
        "AUDIENCE_TYPE_PROSPECTS_BY_AUTOMOBILE_INTENT": "4f2cf7be-4fc1-4992-8663-22ccf114e86d",
        "AUDIENCE_TYPE_PROSPECTS_IN_AUTOMOBILE_MARKET_V3": "8d88d0c4-df8b-418d-b0a3-2a88d6d7a2ad",
    },
    "prod": {
        "AUDIENCE_TYPE_PROSPECTS_IN_AUTOMOBILE_MARKET": "d6c4dd02-7e57-11ee-b962-0242ac120002",
        "AUDIENCE_TYPE_PROSPECTS_BY_INTENT": "479f18c0-5ebf-448f-b7df-d3d3da978e54",
        "AUDIENCE_TYPE_PROSPECTS_LIKELY_HOME_SELLERS": "eff6f0ab-2149-4496-ad12-96d74bb7d672",
        "AUDIENCE_TYPE_PROSPECTS_IN_HOUSING_MARKET": "0813e754-daf0-4cc9-858e-db22e2701feb",
        "AUDIENCE_TYPE_PROSPECTS_IN_AUTOMOBILE_MARKET_V2": "e5168f2e-2a96-4a80-8b32-5f18653cbae9",
        "AUDIENCE_TYPE_PROSPECTS_BY_AUTOMOBILE_INTENT": "4f2cf7be-4fc1-4992-8663-22ccf114e86d",
        "AUDIENCE_TYPE_PROSPECTS_IN_AUTOMOBILE_MARKET_V3": "8d88d0c4-df8b-418d-b0a3-2a88d6d7a2ad",
    },
}

# Signal2segment-specific lambda configurations (DAG-isolated)
SIGNAL2SEGMENT_LAMBDA_CONFIGS = {
    "local": {
        "function_name": "arn:aws:lambda:us-east-1:498598553520:function:dev-quoteprospect",
        "region_name": "us-east-1",
        "invocation_type": "RequestResponse",
        "log_type": "Tail",
        "validation_limit": 1,
        "timeout_seconds": 300,
    },
    "dev": {
        "function_name": "arn:aws:lambda:us-east-1:498598553520:function:dev-quoteprospect",
        "region_name": "us-east-1",
        "invocation_type": "RequestResponse",
        "log_type": "Tail",
        "validation_limit": 3,
        "timeout_seconds": 300,
    },
    "prod": {
        "function_name": "arn:aws:lambda:us-east-1:498598553520:function:prod-quoteprospect",
        "region_name": "us-east-1",
        "invocation_type": "RequestResponse",
        "log_type": "Tail",
        "validation_limit": 3,
        "timeout_seconds": 300,
    },
}


def get_signal2segment_config(environment: Optional[str] = None) -> Dict[str, Any]:
    """
    Get complete signal2segment configuration for the specified environment.

    Args:
        environment: Environment name. If None, uses current environment.

    Returns:
        Dictionary with complete signal2segment configuration
    """
    if environment is None:
        environment = get_current_environment()

    return {
        "lambda_config": SIGNAL2SEGMENT_LAMBDA_CONFIGS.get(
            environment, SIGNAL2SEGMENT_LAMBDA_CONFIGS["dev"]
        ),
        "pygene_conn_id": get_environment_config(
            environment
        ).connections.pygene_conn_id,
        "query_map": SIGNAL2SEGMENT_QUERY_MAPS.get(
            environment, SIGNAL2SEGMENT_QUERY_MAPS["dev"]
        ),
    }


def get_signal2segment_query_map(environment: Optional[str] = None) -> Dict[str, str]:
    """
    Get signal2segment-specific query mappings for the environment.

    Args:
        environment: Environment name. If None, uses current environment.

    Returns:
        Dictionary with query mappings for signal2segment
    """
    if environment is None:
        environment = get_current_environment()

    return SIGNAL2SEGMENT_QUERY_MAPS.get(environment, SIGNAL2SEGMENT_QUERY_MAPS["dev"])


def get_signal2segment_lambda_config(
    environment: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Get signal2segment-specific lambda configuration for the environment.

    Args:
        environment: Environment name. If None, uses current environment.

    Returns:
        Dictionary with lambda configuration for signal2segment
    """
    if environment is None:
        environment = get_current_environment()

    return SIGNAL2SEGMENT_LAMBDA_CONFIGS.get(
        environment, SIGNAL2SEGMENT_LAMBDA_CONFIGS["dev"]
    )


def validate_signal2segment_pygene_credentials(
    environment: Optional[str] = None,
) -> bool:
    """
    Validate PyGene credentials for signal2segment DAG.

    Args:
        environment: Environment name. If None, uses current environment.

    Returns:
        True if credentials are valid, False otherwise
    """
    if environment is None:
        environment = get_current_environment()

    try:
        # Get PyGene connection ID from environment config
        env_config = get_environment_config(environment)
        pygene_conn_id = env_config.connections.pygene_conn_id

        # For now, we'll validate that the connection ID exists
        # In the future, this could validate the actual Airflow connection
        if not pygene_conn_id:
            return False

        # Handle local environment fallback
        if environment == "local":
            # For local development, we assume the connection is valid
            return True
        else:
            # For dev/prod, we could add more sophisticated validation
            # For now, just check that connection ID is not empty
            return bool(pygene_conn_id)

    except Exception:
        return False


def validate_signal2segment_configuration(environment: Optional[str] = None) -> bool:
    """
    Validate complete signal2segment configuration.

    Args:
        environment: Environment name. If None, uses current environment.

    Returns:
        True if configuration is valid, False otherwise
    """
    if environment is None:
        environment = get_current_environment()

    try:
        config = get_signal2segment_config(environment)

        # Validate lambda configuration
        lambda_config = config["lambda_config"]
        if not lambda_config.get("function_name"):
            return False

        # Note: Performance configuration removed - DAG uses hardcoded values in @etdag decorator

        # Validate query map
        query_map = config["query_map"]
        if not query_map or len(query_map) == 0:
            return False

        # Validate PyGene credentials
        if not validate_signal2segment_pygene_credentials(environment):
            return False

        print(f"✅ signal2segment configuration valid for environment '{environment}'")
        return True

    except Exception as e:
        print(
            f"❌ signal2segment configuration validation failed for environment '{environment}': {e}"
        )
        return False
