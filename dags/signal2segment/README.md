# Signal2Segment DAG - Modernized Implementation

This directory contains the modernized implementation of the Signal2Segment DAG, rewritten to follow the new ETDAG standards and Airflow best practices.

## Overview

The Signal2Segment DAG automates the creation and delivery of prospecting audiences based on configurable signal-to-segment rules. It processes audience configurations, validates them through Lambda functions, generates audiences using PyGene API, and delivers them to specified order lines for campaign targeting.

## Architecture

### Modern ETDAG Features

- **@etdag Decorator**: Uses the modern `@etdag` decorator pattern with TaskFlow API
- **Environment-Aware**: Automatically adapts behavior based on environment (local/dev/prod)
- **Lazy Loading**: All expensive operations and imports are inside task functions
- **Absolute Imports**: All imports use absolute paths for Kubernetes compatibility
- **Structured Notifications**: Uses NotificationConfig for environment-aware alerting

### File Structure

```
dags/signal2segment/
├── __init__.py                    # Package initialization
├── README.md                      # This documentation
├── signal2segment_dag.py          # Main DAG definition
├── config.py                      # Configuration and environment-aware settings
└── tasks/                         # Task modules
    ├── __init__.py               # Task package initialization
    ├── validation.py             # Configuration validation tasks
    ├── audience_generation.py    # Audience generation tasks
    └── delivery.py               # Audience delivery tasks
```

## Key Improvements

### 1. Modern Airflow Patterns

- **TaskFlow API**: All tasks use `@task` decorator with proper type hints
- **Automatic XCom**: Data passing handled automatically between tasks
- **Error Handling**: Comprehensive validation and retry logic
- **Performance Monitoring**: Built-in execution time tracking and thresholds

### 2. Environment-Aware Configuration

- **Lazy Loading**: No top-level `Variable.get()` calls
- **Environment Detection**: Automatic environment detection and adaptation
- **Credential Management**: Environment-specific credential handling
- **Lambda Configuration**: Environment-specific Lambda function routing

### 3. Robust Validation

- **Input Validation**: Comprehensive validation of all configuration fields
- **Lambda Validation**: Query argument validation through AWS Lambda
- **Error Notifications**: Structured Slack notifications for validation failures
- **Idempotent Operations**: Safe retry behavior with duplicate detection

### 4. Enhanced Monitoring

- **Structured Logging**: Consistent logging with emojis for easy scanning
- **Performance Tracking**: Execution time monitoring with threshold alerts
- **Success/Failure Notifications**: Environment-aware notification routing
- **Metadata Tracking**: Comprehensive metadata for debugging and auditing

## Configuration

### Environment-Specific Behavior

#### Local Environment
- Uses mock credentials or dev credentials as fallback
- Reduced parallelism (2 concurrent tasks)
- Simplified Lambda validation (limit=1)
- Development notification channels

#### Development Environment
- Uses development PyGene environment
- Moderate parallelism (5 concurrent tasks)
- Full Lambda validation (limit=3)
- Development notification channels

#### Production Environment
- Uses production PyGene environment
- Full parallelism (10 concurrent tasks)
- Full Lambda validation (limit=3)
- Production notification channels + OpsGenie

### Configuration Parameters

The DAG accepts configuration through the `config` parameter:

```json
{
  "config": [
    {
      "org_id": "organization_id",
      "audience_type": "AUDIENCE_TYPE_PROSPECTS_BY_INTENT",
      "query_args": "query_arguments_string",
      "data_source": "data_source_identifier",
      "audience_name_prefix": "audience_name_prefix",
      "automation": {
        "interval_value": 1,
        "interval_unit": "DAYS",
        "type": "REFRESH"
      },
      "destinations": [
        {
          "order_line_id": "order_line_id",
          "is_exclude": false,
          "detach_previous_audience": true
        }
      ]
    }
  ]
}
```

## Usage

### Manual Trigger

The DAG can be triggered manually through the Airflow UI with configuration parameters:

1. Go to the Airflow UI
2. Find the `signal2segment_modernized` DAG
3. Click "Trigger DAG w/ Config"
4. Provide the configuration JSON

### API Trigger

```bash
curl -X POST \
  http://airflow-webserver:8080/api/v1/dags/signal2segment_modernized/dagRuns \
  -H 'Content-Type: application/json' \
  -d '{
    "conf": {
      "config": [
        {
          "org_id": "test_org",
          "audience_type": "AUDIENCE_TYPE_PROSPECTS_BY_INTENT",
          "query_args": "test_args",
          "data_source": "test_source",
          "audience_name_prefix": "test_audience",
          "automation": {
            "interval_value": 1,
            "interval_unit": "DAYS",
            "type": "REFRESH"
          },
          "destinations": [
            {
              "order_line_id": "test_ol_id",
              "is_exclude": false,
              "detach_previous_audience": true
            }
          ]
        }
      ]
    }
  }'
```

## Testing

### Unit Tests

Run the unit tests to verify functionality:

```bash
# Run all tests
pytest tests/test_signal2segment_dag.py -v

# Run DAG loader test
pytest tests/test_signal2segment_dag_loader.py -v

# Run specific test class
pytest tests/test_signal2segment_dag.py::TestConfigurationFunctions -v
```

### DAG Validation

Validate that the DAG loads correctly:

```bash
# Test DAG loading
python dags/signal2segment/signal2segment_dag.py

# Test with Airflow
airflow dags test signal2segment_modernized 2024-01-01
```

## Migration from Legacy DAG

### Key Changes from Old Implementation

1. **DAG Structure**: Migrated from `with ETDAG()` to `@etdag` decorator
2. **Task Definitions**: Converted to pure `@task` decorator with type hints
3. **Configuration**: Replaced top-level `Variable.get()` with lazy loading
4. **Imports**: Converted all relative imports to absolute imports
5. **Notifications**: Replaced manual Slack alerts with NotificationConfig
6. **Error Handling**: Added comprehensive validation and retry logic

### Migration Steps

1. **Test New DAG**: Thoroughly test the new DAG in development environment
2. **Parallel Deployment**: Deploy new DAG alongside old DAG for comparison
3. **Gradual Migration**: Migrate configurations one by one to new DAG
4. **Monitoring**: Monitor performance and success rates
5. **Decommission**: Remove old DAG after successful migration

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed and paths are correct
2. **Configuration Validation**: Check that all required fields are present
3. **Lambda Timeout**: Verify Lambda function is accessible and responsive
4. **PyGene API**: Check credentials and API availability
5. **Notification Failures**: Verify Slack connection and channel permissions

### Debugging

- Check Airflow logs for detailed error messages
- Use structured logging with emojis for easy issue identification
- Monitor performance metrics for threshold violations
- Review notification channels for failure alerts

## Support

For issues or questions:

- **Owner**: data-engineering team
- **Slack Channel**: #signal2segment-alerts
- **Documentation**: This README and inline code documentation
- **Tests**: Comprehensive unit tests in `tests/` directory
