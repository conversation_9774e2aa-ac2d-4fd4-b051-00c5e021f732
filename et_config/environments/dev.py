"""
Development environment configuration for ETDAG framework.

This module defines the configuration for development environment
including dev S3 bucket, k8s connections, and limited UDF availability.
"""

from et_config.models.environment import ConnectionConfig, EnvironmentConfig, S3Config

DEV_CONFIG = EnvironmentConfig(
    name="dev",
    s3=S3Config(
        bucket="et-dev-datalake",
        prefix="{dag_id}",  # Will be replaced with actual dag_id
        region="us-east-1",
    ),
    connections=ConnectionConfig(
        starburst_conn_id="trino_dev",
        slack_conn_id="slack_dev_alerts",
        email_conn_id="email_dev",
        sage_conn_id="sage_dev",
    ),
    hive_catalog="hive",
    iceberg_catalog="iceberg",
    allowed_schemas=["dev_*", "staging_*", "test_*"],
    query_timeout=300,
    max_retries=2,
    is_production=False,
    supported_udfs={
        "ethash_v1": False,  # Not supported in k8s dev environment
        "ethash_v2": False,  # Not supported in k8s dev environment
        "geocodeAddress": False,  # Not supported in k8s dev environment
    },
    # Universal configuration fields
    # Note: No universal configurations currently needed - all moved to DAG-specific folders
)
