"""
ETDAG Decorators for modern DAG definition patterns.

This module provides the @etdag decorator that combines Airflow's @dag decorator
with ETD        # Create default args - avoiding deprecated email settings
        # Note: email_on_* settings are deprecated in Airflow 4.0+ in favor of SmtpNotifier
        default_args = {
            "owner": owner,
            "retries": default_retries,
            "retry_delay": default_retry_delay,
            # TODO: Replace with SmtpNotifier when migrating to Airflow 4.0+
            # For now, we'll handle notifications through our custom handlers
        }idation, safety features, and monitoring capabilities.
"""

import logging
from datetime import timedelta
from typing import Callable, Dict, List, Optional, Union

import pendulum
from airflow.decorators import dag

from et_config import (
    NotificationConfig,
    get_current_environment,
    get_environment_config,
    get_team_emails,
)
from etdag_v2.notifications.opsgenie import OpsGenieNotificationHandler
from etdag_v2.notifications.slack import SlackNotificationHandler
from etdag_v2.validation import (
    DAGValidationError,
    apply_production_safety_checks,
    enhance_tags,
    validate_required_fields,
)

logger = logging.getLogger(__name__)


def etdag(
    dag_id: str,
    owner: str,
    description: str,
    tags: List[str],
    business_purpose: Optional[str] = None,
    data_sources: Optional[List[str]] = None,
    downstream_systems: Optional[List[str]] = None,
    start_date=pendulum.yesterday(),
    schedule: Optional[Union[timedelta, str]] = "@daily",
    max_active_tasks: int = 1,
    max_active_runs: int = 1,
    default_retries: int = 3,
    retry_delay: Optional[timedelta] = None,
    sla_minutes: Optional[int] = None,
    dagrun_timeout: Optional[timedelta] = None,
    notification_config: Optional[NotificationConfig] = None,
    alert_routing: Optional[Dict[str, str]] = None,
    **kwargs,
) -> Callable:
    """
    Enhanced ElToro DAG decorator with validation, safety features, and monitoring.

    This decorator combines Airflow's @dag decorator with ETDAG's enhanced features:
    - Required field validation (owner, description, tags)
    - Default retry configuration
    - Environment-aware alerting
    - Safety checks for production environments
    - Enhanced metadata support
    - Modular notification system

    Args:
        dag_id (str): The id of the DAG; must consist exclusively of alphanumeric characters, dashes, dots and underscores
        owner (str): Required. The owner of the DAG (team or individual)
        description (str): Required. Description of what this DAG does
        tags (List[str]): Required. List of tags for categorization and filtering
        business_purpose (str, optional): Required for production. Business justification for this DAG
        data_sources (List[str], optional): List of data sources consumed by this DAG
        downstream_systems (List[str], optional): List of systems that depend on this DAG's output
        start_date (datetime, optional): Start date for the DAG. Defaults to yesterday
        schedule (Union[timedelta, str], optional): How often to run. Defaults to @daily
        max_active_tasks (int, optional): Max concurrent tasks. Defaults to 1
        max_active_runs (int, optional): Max concurrent DAG runs. Defaults to 1
        default_retries (int, optional): Default number of retries for tasks. Defaults to 3
        retry_delay (timedelta, optional): Delay between retries. Defaults to 5 minutes
        sla_minutes (int, optional): SLA in minutes for the DAG
        dagrun_timeout (timedelta, optional): Timeout for DAG runs
        notification_config (NotificationConfig, optional): Notification settings
        alert_routing (Dict[str, str], optional): Route alerts based on criticality
        **kwargs: Additional arguments passed to base DAG class

    Returns:
        Callable: Decorated function that creates a DAG instance

    Raises:
        DAGValidationError: If required fields are missing or invalid
    """

    def decorator(func: Callable) -> Callable:
        # Get environment configuration
        environment = get_current_environment()
        env_config = get_environment_config()

        # Validate required fields
        validate_required_fields(
            dag_id=dag_id,
            owner=owner,
            description=description,
            tags=tags,
            business_purpose=business_purpose,
        )

        # Production-specific validation
        if env_config.is_prod and not business_purpose:
            raise DAGValidationError(
                f"business_purpose is required for production DAGs. DAG: {dag_id}"
            )

        # Set up notification configuration
        final_notification_config = notification_config or NotificationConfig()

        # Set up notification handlers
        success_callback = None
        failure_callback = None

        if (
            final_notification_config.slack_on_success
            or final_notification_config.slack_on_failure
        ):
            slack_handler = SlackNotificationHandler(
                channel=final_notification_config.slack_channel_override,
                environment=environment,
            )
            if final_notification_config.slack_on_success:
                success_callback = slack_handler.send_success_notification
            if final_notification_config.slack_on_failure:
                failure_callback = slack_handler.send_failure_notification

        if final_notification_config.opsgenie_enabled and env_config.is_prod:
            opsgenie_handler = OpsGenieNotificationHandler(
                team=final_notification_config.team_owner,
                environment=environment,
            )
            # Chain callbacks if both Slack and OpsGenie are enabled
            if failure_callback:
                original_failure = failure_callback

                def combined_failure_callback(context):
                    """Combined failure callback for multiple notification handlers."""
                    original_failure(context)
                    opsgenie_handler.send_failure_notification(context)

                failure_callback = combined_failure_callback
            else:
                failure_callback = opsgenie_handler.send_failure_notification

        # Set up default args
        default_retry_delay = retry_delay or timedelta(minutes=5)
        default_args = {
            "owner": owner,
            "retries": default_retries,
            "retry_delay": default_retry_delay,
            "email_on_failure": final_notification_config.email_on_failure,
            "email_on_retry": False,
            "email_on_success": final_notification_config.email_on_success,
        }

        # Add email destinations if specified
        if final_notification_config.email_destination_override:
            default_args["email"] = final_notification_config.email_destination_override
        elif final_notification_config.team_owner:
            team_emails = get_team_emails(final_notification_config.team_owner)
            if team_emails:
                default_args["email"] = team_emails

        # Prepare DAG parameters for @dag decorator
        dag_params = {
            "dag_id": dag_id,
            "description": description,
            "tags": enhance_tags(tags, owner, environment, business_purpose),
            "start_date": start_date,
            "schedule": schedule,
            "max_active_tasks": max_active_tasks,
            "max_active_runs": max_active_runs,
            "dagrun_timeout": dagrun_timeout,
            "on_success_callback": success_callback,
            "on_failure_callback": failure_callback,
            "default_args": default_args,
            "catchup": kwargs.get("catchup", False),
            "doc_md": kwargs.get("doc_md"),
            "params": kwargs.get("params"),  # Pass through params for manual triggering
        }

        # Note: SLA feature is deprecated in Airflow 3.0+
        # For now, we'll skip SLA configuration to avoid deprecation warnings
        # TODO: Implement new SLA mechanism when available in Airflow 3.1+
        if sla_minutes:
            # Log that SLA is requested but not implemented due to deprecation
            logger.warning(
                f"SLA configuration requested ({sla_minutes} minutes) for DAG '{dag_id}' "
                "but is disabled due to Airflow 3.0+ deprecation. "
                "Will be re-implemented when new SLA mechanism is available."
            )

        # Apply production safety checks
        if env_config.is_prod:
            apply_production_safety_checks(dag_params)

        # Remove None values
        dag_params = {k: v for k, v in dag_params.items() if v is not None}

        # Apply @dag decorator with our enhanced parameters
        @dag(**dag_params)
        def enhanced_dag():
            """Enhanced DAG with ETDAG features."""
            return func()

        # Log DAG creation
        logger.info(
            f"🚀 Created ETDAG '{dag_id}' with enhanced features: "
            f"owner={owner}, env={environment}, "
            f"notifications={'enabled' if final_notification_config else 'disabled'}"
        )

        # The @dag decorator returns a function that when called returns a DAG object
        # For Airflow DAG discovery, we need to call it and return the DAG object
        dag_object = enhanced_dag()

        # Store ETDAG metadata on the DAG object for access
        if hasattr(dag_object, "etdag_metadata"):
            # DAG object already exists, add our metadata
            dag_object.etdag_metadata = {
                "owner": owner,
                "business_purpose": business_purpose,
                "data_sources": data_sources or [],
                "downstream_systems": downstream_systems or [],
                "environment": environment,
                "notification_config": final_notification_config,
                "alert_routing": alert_routing or {},
            }
        else:
            # Set metadata as an attribute
            setattr(
                dag_object,
                "etdag_metadata",
                {
                    "owner": owner,
                    "business_purpose": business_purpose,
                    "data_sources": data_sources or [],
                    "downstream_systems": downstream_systems or [],
                    "environment": environment,
                    "notification_config": final_notification_config,
                    "alert_routing": alert_routing or {},
                },
            )

        return dag_object

    return decorator
