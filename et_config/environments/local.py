"""
Local environment configuration for ETDAG framework.

This module defines the configuration for local development environment
including LocalStack S3, local connections, and UDF availability.
"""

from et_config.models.environment import ConnectionConfig, EnvironmentConfig, S3Config

LOCAL_CONFIG = EnvironmentConfig(
    name="local",
    s3=S3Config(
        bucket="localstack-bucket", prefix="local-dev/{dag_id}", region="us-east-1"
    ),
    connections=ConnectionConfig(
        starburst_conn_id="trino_local",
        slack_conn_id="slack_local",
        email_conn_id="email_local",
        sage_conn_id="sage_local",
    ),
    hive_catalog="s3",
    iceberg_catalog="olympus",
    allowed_schemas=["default", "test"],
    query_timeout=30,
    max_retries=1,
    is_production=False,
    supported_udfs={
        "ethash_v1": False,  # Not supported in local environment
        "ethash_v2": False,  # Not supported in local environment
        "geocodeAddress": False,  # Not supported in local environment
    },
    # Universal configuration fields
    # Note: No universal configurations currently needed - all moved to DAG-specific folders
)
