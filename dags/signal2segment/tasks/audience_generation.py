"""
Audience generation tasks for Signal2Segment DAG.

This module contains tasks for generating prospecting audiences using the PyGene API,
following modern TaskFlow API patterns with proper error handling and retry logic.
"""

import logging
from typing import Dict, Any

from airflow.decorators import task
from airflow.exceptions import AirflowFailException

from et_config import get_pygene_credentials

logger = logging.getLogger(__name__)


@task.virtualenv(
    requirements=["pygene>=1.0.34"],
    system_site_packages=True,
    pip_install_options=["--upgrade"],
)
def generate_audience(validated_config: Dict[str, Any], **context) -> Dict[str, Any]:
    """
    Generate a prospecting audience using PyGene API.

    This task creates a new prospecting audience based on the validated configuration,
    using the PyGene Audience API with proper error handling and retry logic.

    Args:
        validated_config: Validated configuration dictionary from validation task
        **context: Airflow context containing execution date and other metadata

    Returns:
        Dictionary containing audience ID and configuration details

    Raises:
        AirflowFailException: If audience generation fails
    """
    # Import PyGene modules inside task to avoid top-level imports
    from pygene.audience_api import (
        NextGenAudienceAPI,
        Audience,
        AudienceType,
        Automation,
        AutomationIntervalUnit,
        AutomationType,
    )
    from pygene.target_api import NextGenTargetAPI
    from et_config.environments.registry import get_current_environment

    logger.info(
        f"🎯 Starting audience generation for org_id: {validated_config['org_id']}"
    )

    # Performance monitoring
    import time

    start_time = time.time()

    try:
        # Get environment and credentials
        env = get_current_environment()
        credentials = get_pygene_credentials()

        # Get execution date for audience naming
        logical_date = context["logical_date"]
        today = logical_date.date()

        logger.info(f"🌍 Running in {env} environment for date: {today}")

        # Initialize Target API
        logger.info("🎯 Creating fileless target")
        target_api = NextGenTargetAPI(
            client_id=credentials["client_id"],
            client_secret=credentials["client_secret"],
            org_id=validated_config["org_id"],
            env=env,
        )

        target = target_api.upload_fileless_targets()
        logger.info(f"✅ Created target with ID: {target.id}")

        # Initialize Audience API
        audience_api = NextGenAudienceAPI(
            client_id=credentials["client_id"],
            client_secret=credentials["client_secret"],
            org_id=validated_config["org_id"],
            env=env,
        )

        # Parse audience type
        audience_type_str = _parse_audience_type(validated_config["audience_type"])
        audience_type = AudienceType[audience_type_str]

        logger.info(f"📊 Generating audience of type: {audience_type_str}")

        # Create audience configuration
        audience_config = {
            "target_id": target.id,
            "type": audience_type,
            "name": validated_config["audience_name_prefix"],
            "org_id": validated_config["org_id"],
            "data_source": validated_config["data_source"],
            "audiences": [
                Audience(
                    {
                        "type": AudienceType.ADDRESS,
                        "name": f"{validated_config['audience_name_prefix']}-{today}",
                        "data_source": validated_config["data_source"],
                    }
                )
            ],
        }

        # Add automation if specified
        if "automation" in validated_config:
            automation_config = validated_config["automation"]
            audience_config["automations"] = [
                Automation(
                    {
                        "enabled": True,
                        "interval_value": automation_config["interval_value"],
                        "interval_unit": AutomationIntervalUnit[
                            automation_config["interval_unit"]
                        ],
                        "type": AutomationType[automation_config["type"]],
                    }
                )
            ]

        # Create audience
        audience = Audience(audience_config)

        logger.info("🚀 Creating audience with PyGene API")
        audience_result = audience_api.create_any_audience(
            poll=True,
            generate=True,
            audience=audience,
            query_args=validated_config["query_args"],
        )

        # Extract sub-audience ID
        if not audience_result.sub_audiences:
            raise ValueError("No sub-audiences created")

        sub_audience = audience_result.sub_audiences[0]
        sub_audience_id = sub_audience["id"]

        logger.info(
            f"✅ Successfully created audience with sub-audience ID: {sub_audience_id}"
        )

        # Performance monitoring
        duration = time.time() - start_time
        logger.info(f"📈 Audience generation completed in {duration:.2f} seconds")

        # Performance threshold warning (5 minutes)
        if duration > 300:
            logger.warning(
                f"⚠️ Audience generation exceeded performance threshold: {duration:.2f}s > 300s"
            )

        # Prepare result
        result = {
            "audience_id": sub_audience_id,
            "target_id": target.id,
            "config": validated_config,
            "generation_metadata": {
                "audience_type": audience_type_str,
                "generated_at": "{{ ts }}",
                "environment": env,
                "sub_audiences": audience_result.sub_audiences,
                "performance": {
                    "duration_seconds": duration,
                    "threshold_exceeded": duration > 300,
                },
            },
        }

        return result

    except Exception as e:
        duration = time.time() - start_time
        error_message = f"Error generating audience: {str(e)}"
        logger.error(f"❌ {error_message}")
        logger.error(f"📊 Task failed after {duration:.2f} seconds")

        # Failure notifications are handled by etdag_v2 built-in messaging

        raise AirflowFailException(error_message)


def _parse_audience_type(audience_type: str) -> str:
    """
    Parse audience type string to extract the enum-compatible format.

    Args:
        audience_type: Full audience type string (e.g., "AUDIENCE_TYPE_PROSPECTS_BY_INTENT")

    Returns:
        Parsed audience type string for enum lookup
    """
    # Remove "AUDIENCE_TYPE_" prefix and return the rest
    if audience_type.startswith("AUDIENCE_TYPE_"):
        return audience_type[14:]  # Remove "AUDIENCE_TYPE_" (14 characters)
    return audience_type
